"""
AI API client for EMDrafter.
Handles AI-powered email reply generation with support for multiple providers.
Supports Deepseek and OpenRouter APIs.
"""

import requests
import json
import logging
from typing import Optional, Tuple, Dict, Any, List
from config import config

logger = logging.getLogger(__name__)

class AIClient:
    """Client for interacting with AI APIs (Deepseek and OpenRouter)."""

    def __init__(self):
        """Initialize the AI client."""
        self.deepseek_api_key = config.deepseek_api_key
        self.openrouter_api_key = getattr(config, 'openrouter_api_key', '')
        self.deepseek_api_base = config.deepseek_api_base
        self.openrouter_api_base = "https://openrouter.ai"
        self.timeout = config.api_timeout
        self.max_conversation_length = config.max_conversation_length

        # Current provider settings
        self.current_provider = "deepseek"  # Default to deepseek
        self.current_model = "deepseek-chat"  # Default model

    def set_provider(self, provider: str, model: str = None) -> None:
        """
        Set the AI provider and model.

        Args:
            provider: Either 'deepseek' or 'openrouter'
            model: Model name (required for openrouter, optional for deepseek)
        """
        self.current_provider = provider.lower()

        if provider.lower() == "deepseek":
            self.current_model = "deepseek-chat"
        elif provider.lower() == "openrouter":
            if not model:
                raise ValueError("Model must be specified for OpenRouter")
            self.current_model = model
        else:
            raise ValueError(f"Unsupported provider: {provider}")

    def get_available_openrouter_models(self) -> Tuple[bool, List[Dict], str]:
        """
        Get list of available models from OpenRouter.

        Returns:
            tuple: (success, models_list, error_message)
        """
        if not self.openrouter_api_key:
            return False, [], "OpenRouter API key not configured"

        try:
            headers = {
                'Authorization': f'Bearer {self.openrouter_api_key}',
                'Content-Type': 'application/json'
            }

            response = requests.get(
                f"{self.openrouter_api_base}/v1/models",
                headers=headers,
                timeout=self.timeout
            )

            if response.status_code == 200:
                models_data = response.json()
                models = models_data.get('data', [])

                # Filter and sort models for better usability
                filtered_models = []
                for model in models:
                    model_info = {
                        'id': model.get('id', ''),
                        'name': model.get('name', model.get('id', '')),
                        'description': model.get('description', ''),
                        'pricing': model.get('pricing', {})
                    }
                    filtered_models.append(model_info)

                # Sort by name for easier selection
                filtered_models.sort(key=lambda x: x['name'].lower())

                return True, filtered_models, ""
            else:
                return False, [], f"HTTP {response.status_code}: {response.text}"

        except Exception as e:
            logger.error(f"Error fetching OpenRouter models: {str(e)}")
            return False, [], f"Error fetching models: {str(e)}"

    def validate_api_key(self, provider: str = None) -> Tuple[bool, str]:
        """
        Validate the API key by making a test request.

        Args:
            provider: Provider to validate ('deepseek' or 'openrouter'). Uses current if None.

        Returns:
            tuple: (is_valid, error_message)
        """
        test_provider = provider or self.current_provider

        if test_provider == "deepseek":
            return self._validate_deepseek_key()
        elif test_provider == "openrouter":
            return self._validate_openrouter_key()
        else:
            return False, f"Unknown provider: {test_provider}"

    def _validate_deepseek_key(self) -> Tuple[bool, str]:
        """Validate Deepseek API key."""
        if not self.deepseek_api_key:
            return False, "Deepseek API key not configured"

        try:
            headers = {
                'Authorization': f'Bearer {self.deepseek_api_key}',
                'Content-Type': 'application/json'
            }

            test_payload = {
                "model": "deepseek-chat",
                "messages": [
                    {"role": "user", "content": "Hello"}
                ],
                "max_tokens": 10
            }

            response = requests.post(
                f"{self.deepseek_api_base}/v1/chat/completions",
                headers=headers,
                json=test_payload,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                return True, "API key is valid"
            elif response.status_code == 401:
                return False, "Invalid API key"
            else:
                return False, f"API error: {response.status_code}"
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Deepseek API validation error: {str(e)}")
            return False, f"Connection error: {str(e)}"

    def _validate_openrouter_key(self) -> Tuple[bool, str]:
        """Validate OpenRouter API key."""
        if not self.openrouter_api_key:
            return False, "OpenRouter API key not configured"

        try:
            headers = {
                'Authorization': f'Bearer {self.openrouter_api_key}',
                'Content-Type': 'application/json'
            }

            # Use a simple, cheap model for testing
            test_payload = {
                "model": "openai/gpt-3.5-turbo",
                "messages": [
                    {"role": "user", "content": "Hello"}
                ],
                "max_tokens": 10
            }

            response = requests.post(
                f"{self.openrouter_api_base}/v1/chat/completions",
                headers=headers,
                json=test_payload,
                timeout=self.timeout
            )

            if response.status_code == 200:
                return True, "OpenRouter API key is valid"
            elif response.status_code == 401:
                return False, "Invalid OpenRouter API key"
            elif response.status_code == 402:
                return False, "OpenRouter API key has insufficient credits"
            else:
                return False, f"OpenRouter API error: {response.status_code}"

        except requests.exceptions.RequestException as e:
            logger.error(f"OpenRouter API validation error: {str(e)}")
            return False, f"Connection error: {str(e)}"
    
    def generate_reply(self, conversation_text: str, 
                      custom_prompt: Optional[str] = None) -> Tuple[bool, str, str]:
        """
        Generate an AI reply based on conversation context.
        
        Args:
            conversation_text: The formatted conversation history
            custom_prompt: Optional custom prompt for reply generation
            
        Returns:
            tuple: (success, generated_reply, error_message)
        """
        try:
            # Check API key based on current provider
            if self.current_provider == "deepseek" and not self.deepseek_api_key:
                return False, "", "Deepseek API key not configured"
            elif self.current_provider == "openrouter" and not self.openrouter_api_key:
                return False, "", "OpenRouter API key not configured"

            # Prepare the conversation for AI processing
            processed_conversation = self._process_conversation(conversation_text)

            # Create the prompt
            prompt = self._create_prompt(processed_conversation, custom_prompt)

            # Make API request based on provider
            success, response_data, error = self._make_api_request(prompt)
            
            if not success:
                return False, "", error
            
            # Extract the generated reply
            reply = self._extract_reply(response_data)
            
            if not reply:
                return False, "", "Failed to extract reply from API response"
            
            logger.info("Successfully generated AI reply")
            return True, reply, ""
            
        except Exception as e:
            logger.error(f"Error generating reply: {str(e)}")
            return False, "", f"Error generating reply: {str(e)}"
    
    def _process_conversation(self, conversation_text: str) -> str:
        """
        Process conversation text to fit within token limits.
        
        Args:
            conversation_text: Raw conversation text
            
        Returns:
            Processed conversation text
        """
        # If conversation is too long, truncate from the beginning
        # but keep the most recent messages
        if len(conversation_text) > self.max_conversation_length:
            logger.warning(f"Conversation too long ({len(conversation_text)} chars), truncating")
            
            # Find a good truncation point (try to keep complete messages)
            truncated = conversation_text[-self.max_conversation_length:]
            
            # Try to find the start of a complete message
            message_start = truncated.find("Message ")
            if message_start > 0:
                truncated = truncated[message_start:]
            
            truncated = "[...conversation truncated...]\n\n" + truncated
            return truncated
        
        return conversation_text
    
    def _create_prompt(self, conversation_text: str, custom_prompt: Optional[str] = None) -> str:
        """
        Create the prompt for AI reply generation.
        
        Args:
            conversation_text: Processed conversation text
            custom_prompt: Optional custom prompt
            
        Returns:
            Complete prompt for AI
        """
        if custom_prompt:
            base_prompt = custom_prompt
        else:
            base_prompt = """Based on the following email conversation, write a polite, professional, and context-aware reply. 
The reply should:
1. Address the main points from the most recent message
2. Maintain a professional but friendly tone
3. Be concise and to the point
4. Include appropriate greetings and closings
5. Be helpful and constructive

Please write only the email reply content, without any additional commentary or explanations."""
        
        full_prompt = f"{base_prompt}\n\nEmail Conversation:\n{conversation_text}\n\nYour Reply:"
        
        return full_prompt
    
    def _make_api_request(self, prompt: str) -> Tuple[bool, Optional[Dict[Any, Any]], str]:
        """
        Make the actual API request to the selected AI provider.

        Args:
            prompt: The complete prompt for AI

        Returns:
            tuple: (success, response_data, error_message)
        """
        try:
            if self.current_provider == "deepseek":
                return self._make_deepseek_request(prompt)
            elif self.current_provider == "openrouter":
                return self._make_openrouter_request(prompt)
            else:
                return False, None, f"Unsupported provider: {self.current_provider}"

        except Exception as e:
            logger.error(f"API request error: {str(e)}")
            return False, None, f"Request error: {str(e)}"

    def _make_deepseek_request(self, prompt: str) -> Tuple[bool, Optional[Dict[Any, Any]], str]:
        """Make API request to Deepseek."""
        try:
            headers = {
                'Authorization': f'Bearer {self.deepseek_api_key}',
                'Content-Type': 'application/json'
            }

            payload = {
                "model": "deepseek-chat",
                "messages": [
                    {
                        "role": "system",
                        "content": "You are a helpful email assistant that writes professional and contextually appropriate email replies."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "max_tokens": 1000,
                "temperature": 0.7,
                "top_p": 0.9
            }
            
            logger.info("Making API request to Deepseek")
            response = requests.post(
                f"{self.deepseek_api_base}/v1/chat/completions",
                headers=headers,
                json=payload,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                return True, response.json(), ""
            else:
                error_msg = f"API request failed with status {response.status_code}"
                try:
                    error_data = response.json()
                    if 'error' in error_data:
                        error_msg += f": {error_data['error'].get('message', 'Unknown error')}"
                except:
                    error_msg += f": {response.text}"
                
                logger.error(error_msg)
                return False, None, error_msg
                
        except requests.exceptions.Timeout:
            error_msg = "API request timed out"
            logger.error(error_msg)
            return False, None, error_msg
        except requests.exceptions.RequestException as e:
            error_msg = f"Deepseek API request failed: {str(e)}"
            logger.error(error_msg)
            return False, None, error_msg

    def _make_openrouter_request(self, prompt: str) -> Tuple[bool, Optional[Dict[Any, Any]], str]:
        """Make API request to OpenRouter."""
        try:
            headers = {
                'Authorization': f'Bearer {self.openrouter_api_key}',
                'Content-Type': 'application/json',
                'HTTP-Referer': 'https://github.com/your-repo/EMDrafter',  # Optional: for analytics
                'X-Title': 'EMDrafter'  # Optional: for analytics
            }

            payload = {
                "model": self.current_model,
                "messages": [
                    {
                        "role": "system",
                        "content": "You are a helpful email assistant that writes professional and contextually appropriate email replies."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "max_tokens": 1000,
                "temperature": 0.7,
                "top_p": 0.9
            }

            logger.info(f"Making API request to OpenRouter with model: {self.current_model}")
            response = requests.post(
                f"{self.openrouter_api_base}/v1/chat/completions",
                headers=headers,
                json=payload,
                timeout=self.timeout
            )

            if response.status_code == 200:
                return True, response.json(), ""
            else:
                error_msg = f"OpenRouter API request failed with status {response.status_code}"
                try:
                    error_data = response.json()
                    if 'error' in error_data:
                        error_msg += f": {error_data['error'].get('message', 'Unknown error')}"
                except:
                    error_msg += f": {response.text}"

                logger.error(error_msg)
                return False, None, error_msg

        except requests.exceptions.Timeout:
            error_msg = "OpenRouter API request timed out"
            logger.error(error_msg)
            return False, None, error_msg
        except requests.exceptions.RequestException as e:
            error_msg = f"OpenRouter API request failed: {str(e)}"
            logger.error(error_msg)
            return False, None, error_msg
    
    def _extract_reply(self, response_data: Dict[Any, Any]) -> Optional[str]:
        """
        Extract the generated reply from API response.
        
        Args:
            response_data: API response data
            
        Returns:
            Generated reply text or None if extraction failed
        """
        try:
            if 'choices' not in response_data or not response_data['choices']:
                logger.error("No choices in API response")
                return None
            
            choice = response_data['choices'][0]
            
            if 'message' not in choice or 'content' not in choice['message']:
                logger.error("No message content in API response")
                return None
            
            reply = choice['message']['content'].strip()
            
            # Clean up the reply
            reply = self._clean_reply(reply)
            
            return reply
            
        except Exception as e:
            logger.error(f"Error extracting reply: {str(e)}")
            return None
    
    def _clean_reply(self, reply: str) -> str:
        """
        Clean up the generated reply.
        
        Args:
            reply: Raw generated reply
            
        Returns:
            Cleaned reply
        """
        # Remove any leading/trailing whitespace
        reply = reply.strip()
        
        # Remove common AI response prefixes
        prefixes_to_remove = [
            "Here's a reply:",
            "Here is a reply:",
            "Reply:",
            "Email reply:",
            "Response:",
        ]
        
        for prefix in prefixes_to_remove:
            if reply.lower().startswith(prefix.lower()):
                reply = reply[len(prefix):].strip()
        
        return reply

# Global AI client instance (supports both Deepseek and OpenRouter)
ai_client = AIClient()

# Backward compatibility alias
deepseek_client = ai_client
