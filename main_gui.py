"""
Main GUI application for EMDrafter.
Windows-compatible tkinter interface for AI-powered email reply drafting.
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import logging
from typing import Optional, List, Dict
from config import config
from gmail_auth import gmail_auth
from email_retrieval import conversation_retriever, EmailMessage
from deepseek_client import deepseek_client
from email_operations import email_ops
from error_handler import error_handler, handle_exceptions
from security import input_validator, secure_storage

logger = logging.getLogger(__name__)

class EMDrafterGUI:
    """Main GUI application class."""
    
    def __init__(self):
        """Initialize the GUI application."""
        self.root = tk.Tk()
        self.root.title(config.get_window_title())
        self.root.geometry("1000x800")
        self.root.minsize(800, 600)
        
        # Configure style for Windows
        self.style = ttk.Style()
        if "winnative" in self.style.theme_names():
            self.style.theme_use("winnative")
        
        # Application state
        self.current_messages: List[EmailMessage] = []
        self.current_user_email: str = ""
        self.current_password: str = ""
        self.current_target_email: str = ""
        self.generated_reply: str = ""
        self.selected_message_index: int = -1  # Index of selected message for reply

        # AI provider settings
        self.current_ai_provider: str = "deepseek"
        self.current_ai_model: str = "deepseek-chat"
        self.available_openrouter_models: List[Dict] = []
        
        # Create GUI components
        self._create_widgets()
        self._setup_layout()
        
        # Status
        self.update_status("Ready")
        
        # Validate configuration on startup
        self._validate_startup_config()
    
    def _create_widgets(self) -> None:
        """Create all GUI widgets."""
        # Main frame
        self.main_frame = ttk.Frame(self.root, padding="10")
        
        # Authentication frame
        self.auth_frame = ttk.LabelFrame(self.main_frame, text="Gmail Authentication", padding="10")
        
        # Email input
        ttk.Label(self.auth_frame, text="Gmail Address:").grid(row=0, column=0, sticky="w", pady=2)
        self.email_var = tk.StringVar()
        self.email_entry = ttk.Entry(self.auth_frame, textvariable=self.email_var, width=40)
        
        # Password input
        ttk.Label(self.auth_frame, text="Password/App Password:").grid(row=1, column=0, sticky="w", pady=2)
        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(self.auth_frame, textvariable=self.password_var, show="*", width=40)
        
        # Test connection button
        self.test_auth_btn = ttk.Button(self.auth_frame, text="Test Connection", command=self._test_authentication)

        # AI Provider frame
        self.ai_frame = ttk.LabelFrame(self.main_frame, text="AI Provider Settings", padding="10")

        # Provider selection
        ttk.Label(self.ai_frame, text="AI Provider:").grid(row=0, column=0, sticky="w", pady=2)
        self.provider_var = tk.StringVar(value="deepseek")
        self.provider_combo = ttk.Combobox(self.ai_frame, textvariable=self.provider_var,
                                          values=["deepseek", "openrouter"], state="readonly", width=15)
        self.provider_combo.bind('<<ComboboxSelected>>', self._on_provider_change)

        # Model selection (for OpenRouter)
        ttk.Label(self.ai_frame, text="Model:").grid(row=1, column=0, sticky="w", pady=2)
        self.model_var = tk.StringVar(value="deepseek-chat")
        self.model_combo = ttk.Combobox(self.ai_frame, textvariable=self.model_var,
                                       state="readonly", width=30)

        # Load models button (for OpenRouter)
        self.load_models_btn = ttk.Button(self.ai_frame, text="Load Models",
                                         command=self._load_openrouter_models, state="disabled")

        # Conversation frame
        self.conv_frame = ttk.LabelFrame(self.main_frame, text="Email Conversation", padding="10")
        
        # Target email input
        ttk.Label(self.conv_frame, text="Email Address to Search:").grid(row=0, column=0, sticky="w", pady=2)
        self.target_email_var = tk.StringVar()
        self.target_email_entry = ttk.Entry(self.conv_frame, textvariable=self.target_email_var, width=40)
        
        # Fetch conversation button
        self.fetch_conv_btn = ttk.Button(self.conv_frame, text="Fetch Conversation", command=self._fetch_conversation)
        
        # Conversation display
        ttk.Label(self.conv_frame, text="Conversation History:").grid(row=2, column=0, sticky="w", pady=(10,2))

        # Message selection frame
        self.message_selection_frame = ttk.Frame(self.conv_frame)
        ttk.Label(self.message_selection_frame, text="Select message to reply to:").pack(anchor="w")

        # Message selection listbox with scrollbar
        self.message_list_frame = ttk.Frame(self.message_selection_frame)
        self.message_listbox = tk.Listbox(self.message_list_frame, height=4, selectmode=tk.SINGLE)
        self.message_scrollbar = ttk.Scrollbar(self.message_list_frame, orient="vertical", command=self.message_listbox.yview)
        self.message_listbox.configure(yscrollcommand=self.message_scrollbar.set)
        self.message_listbox.bind('<<ListboxSelect>>', self._on_message_select)

        # Pack message selection components
        self.message_listbox.pack(side="left", fill="both", expand=True)
        self.message_scrollbar.pack(side="right", fill="y")
        self.message_list_frame.pack(fill="x", pady=(5,10))

        # Full conversation display
        self.conversation_text = scrolledtext.ScrolledText(self.conv_frame, height=12, width=80, wrap=tk.WORD)
        
        # AI Reply frame
        self.reply_frame = ttk.LabelFrame(self.main_frame, text="AI Reply Generation", padding="10")
        
        # Generate reply button
        self.generate_reply_btn = ttk.Button(self.reply_frame, text="Draft AI Reply", command=self._generate_reply)
        
        # Reply display and editing
        ttk.Label(self.reply_frame, text="Generated Reply:").grid(row=1, column=0, sticky="w", pady=(10,2))
        self.reply_text = scrolledtext.ScrolledText(self.reply_frame, height=8, width=80, wrap=tk.WORD)
        
        # Action buttons frame
        self.action_frame = ttk.Frame(self.reply_frame)
        self.send_reply_btn = ttk.Button(self.action_frame, text="Send Reply", command=self._send_reply)
        self.save_draft_btn = ttk.Button(self.action_frame, text="Save Draft", command=self._save_draft)
        
        # Status frame
        self.status_frame = ttk.Frame(self.main_frame)
        self.status_var = tk.StringVar()
        self.status_label = ttk.Label(self.status_frame, textvariable=self.status_var, relief="sunken", anchor="w")
        
        # Progress bar
        self.progress = ttk.Progressbar(self.status_frame, mode='indeterminate')
    
    def _setup_layout(self) -> None:
        """Setup the layout of all widgets."""
        # Main frame
        self.main_frame.pack(fill="both", expand=True)
        
        # Authentication frame
        self.auth_frame.pack(fill="x", pady=(0,10))
        self.email_entry.grid(row=0, column=1, sticky="ew", padx=(10,0), pady=2)
        self.password_entry.grid(row=1, column=1, sticky="ew", padx=(10,0), pady=2)
        self.test_auth_btn.grid(row=0, column=2, rowspan=2, padx=(10,0), pady=2)
        
        # Configure column weights
        self.auth_frame.columnconfigure(1, weight=1)

        # AI Provider frame
        self.ai_frame.pack(fill="x", pady=(0,10))
        self.provider_combo.grid(row=0, column=1, sticky="w", padx=(10,0), pady=2)
        self.model_combo.grid(row=1, column=1, sticky="ew", padx=(10,0), pady=2)
        self.load_models_btn.grid(row=1, column=2, padx=(10,0), pady=2)

        # Configure AI frame column weights
        self.ai_frame.columnconfigure(1, weight=1)

        # Conversation frame
        self.conv_frame.pack(fill="both", expand=True, pady=(0,10))
        self.target_email_entry.grid(row=0, column=1, sticky="ew", padx=(10,0), pady=2)
        self.fetch_conv_btn.grid(row=0, column=2, padx=(10,0), pady=2)
        self.message_selection_frame.grid(row=3, column=0, columnspan=3, sticky="ew", pady=(2,0))
        self.conversation_text.grid(row=4, column=0, columnspan=3, sticky="nsew", pady=(2,0))
        
        # Configure conversation frame
        self.conv_frame.columnconfigure(1, weight=1)
        self.conv_frame.rowconfigure(4, weight=1)  # Updated for new row layout
        
        # Reply frame
        self.reply_frame.pack(fill="both", expand=True, pady=(0,10))
        self.generate_reply_btn.grid(row=0, column=0, pady=2)
        self.reply_text.grid(row=2, column=0, columnspan=2, sticky="nsew", pady=(2,10))
        
        # Action buttons
        self.action_frame.grid(row=3, column=0, columnspan=2, sticky="ew")
        self.send_reply_btn.pack(side="left", padx=(0,10))
        self.save_draft_btn.pack(side="left")
        
        # Configure reply frame
        self.reply_frame.columnconfigure(0, weight=1)
        self.reply_frame.rowconfigure(2, weight=1)
        
        # Status frame
        self.status_frame.pack(fill="x", pady=(10,0))
        self.status_label.pack(side="left", fill="x", expand=True)
        self.progress.pack(side="right", padx=(10,0))
    
    def _validate_startup_config(self) -> None:
        """Validate configuration on startup."""
        is_valid, errors = config.validate_config()
        if not is_valid:
            error_msg = "Configuration errors found:\n" + "\n".join(errors)
            messagebox.showwarning("Configuration Warning", error_msg)
            self.update_status("Configuration issues detected - check .env file")
    
    def update_status(self, message: str) -> None:
        """Update status message."""
        self.status_var.set(message)
        self.root.update_idletasks()
        logger.info(f"Status: {message}")
    
    def show_progress(self, show: bool = True) -> None:
        """Show or hide progress indicator."""
        if show:
            self.progress.start()
        else:
            self.progress.stop()
    
    @handle_exceptions(context="authentication", user_action="testing Gmail connection")
    def _test_authentication(self) -> None:
        """Test Gmail authentication."""
        email = self.email_var.get().strip()
        password = self.password_var.get()

        # Validate inputs
        if not email or not password:
            messagebox.showerror("Error", "Please enter both email and password")
            return

        # Validate email format
        is_valid, error_msg = input_validator.validate_email(email)
        if not is_valid:
            messagebox.showerror("Invalid Email", error_msg)
            return

        # Validate password
        is_valid, error_msg = input_validator.validate_password(password)
        if not is_valid:
            messagebox.showerror("Invalid Password", error_msg)
            return
        
        def test_auth():
            self.show_progress(True)
            self.update_status("Testing authentication...")
            
            success, error_msg = gmail_auth.authenticate(email, password)
            
            self.show_progress(False)
            
            if success:
                self.current_user_email = email
                # Store password securely in memory
                secure_storage.store_temporarily("user_password", password, encrypt=True)
                self.current_password = password  # Keep for immediate use
                self.update_status("Authentication successful")
                messagebox.showinfo("Success", "Gmail authentication successful!")
            else:
                self.update_status("Authentication failed")
                messagebox.showerror("Authentication Failed", error_msg)
        
        # Run in separate thread to prevent GUI freezing
        threading.Thread(target=test_auth, daemon=True).start()
    
    @handle_exceptions(context="email retrieval", user_action="fetching conversation")
    def _fetch_conversation(self) -> None:
        """Fetch email conversation with target email."""
        if not self.current_user_email or not self.current_password:
            messagebox.showerror("Error", "Please authenticate with Gmail first")
            return

        target_email = self.target_email_var.get().strip()
        if not target_email:
            messagebox.showerror("Error", "Please enter an email address to search")
            return

        # Validate target email format
        is_valid, error_msg = input_validator.validate_email(target_email)
        if not is_valid:
            messagebox.showerror("Invalid Email", error_msg)
            return
        
        def fetch_conv():
            self.show_progress(True)
            self.update_status("Fetching conversation...")
            
            success, messages, error_msg = conversation_retriever.search_conversations(
                self.current_user_email, self.current_password, target_email, limit=10
            )
            
            self.show_progress(False)
            
            if success:
                self.current_messages = messages
                self.current_target_email = target_email

                if messages:
                    # Populate message selection listbox
                    self.message_listbox.delete(0, tk.END)
                    for i, msg in enumerate(messages):
                        sender = "You" if msg.sender == self.current_user_email else msg.sender
                        date_str = msg.date.strftime('%Y-%m-%d %H:%M')
                        subject_preview = msg.subject[:50] + "..." if len(msg.subject) > 50 else msg.subject
                        list_item = f"{i+1}. {sender} - {date_str} - {subject_preview}"
                        self.message_listbox.insert(tk.END, list_item)

                    # Auto-select the last message (most recent)
                    if len(messages) > 0:
                        self.message_listbox.selection_set(len(messages) - 1)
                        self.selected_message_index = len(messages) - 1

                    # Format and display full conversation
                    formatted_conv = conversation_retriever.format_conversation(messages, self.current_user_email)
                    self.conversation_text.delete(1.0, tk.END)
                    self.conversation_text.insert(1.0, formatted_conv)

                    # Update status
                    if len(messages) > 1:
                        self.update_status(f"Found {len(messages)} messages. Select a message above to reply to.")
                    else:
                        self.update_status(f"Found {len(messages)} message in conversation")
                else:
                    # Clear message list and conversation
                    self.message_listbox.delete(0, tk.END)
                    self.conversation_text.delete(1.0, tk.END)
                    self.conversation_text.insert(1.0, "No conversation found with this email address.")
                    self.selected_message_index = -1
                    self.update_status("No messages found")
            else:
                self.update_status("Failed to fetch conversation")
                messagebox.showerror("Error", f"Failed to fetch conversation: {error_msg}")
        
        # Run in separate thread
        threading.Thread(target=fetch_conv, daemon=True).start()

    def _on_message_select(self, event) -> None:
        """Handle message selection from listbox."""
        selection = self.message_listbox.curselection()
        if selection:
            self.selected_message_index = selection[0]
            selected_msg = self.current_messages[self.selected_message_index]

            # Update status to show which message is selected
            sender = "You" if selected_msg.sender == self.current_user_email else selected_msg.sender
            self.update_status(f"Selected message from {sender} - {selected_msg.date.strftime('%Y-%m-%d %H:%M')}")
        else:
            self.selected_message_index = -1

    def _on_provider_change(self, event) -> None:
        """Handle AI provider selection change."""
        provider = self.provider_var.get().lower()
        self.current_ai_provider = provider

        if provider == "deepseek":
            # Set default Deepseek model
            self.current_ai_model = "deepseek-chat"
            self.model_var.set("deepseek-chat")
            self.model_combo.configure(values=["deepseek-chat"], state="readonly")
            self.load_models_btn.configure(state="disabled")

            # Update AI client
            deepseek_client.set_provider("deepseek")
            self.update_status("Switched to Deepseek provider")

        elif provider == "openrouter":
            # Enable model loading for OpenRouter
            self.model_combo.configure(values=[], state="readonly")
            self.model_var.set("")
            self.load_models_btn.configure(state="normal")
            self.update_status("OpenRouter selected - click 'Load Models' to see available models")

    def _load_openrouter_models(self) -> None:
        """Load available models from OpenRouter."""
        def load_models():
            self.show_progress(True)
            self.update_status("Loading OpenRouter models...")

            success, models, error_msg = deepseek_client.get_available_openrouter_models()

            self.show_progress(False)

            if success:
                self.available_openrouter_models = models
                model_names = [f"{model['id']}" for model in models]

                # Update combobox with model names
                self.model_combo.configure(values=model_names)

                if model_names:
                    # Set first model as default
                    self.model_var.set(model_names[0])
                    self.current_ai_model = model_names[0]
                    deepseek_client.set_provider("openrouter", model_names[0])
                    self.update_status(f"Loaded {len(models)} OpenRouter models")
                else:
                    self.update_status("No models available from OpenRouter")
            else:
                self.update_status("Failed to load OpenRouter models")
                messagebox.showerror("Error", f"Failed to load OpenRouter models: {error_msg}")

        # Run in separate thread
        threading.Thread(target=load_models, daemon=True).start()

    def _generate_reply(self) -> None:
        """Generate AI reply based on conversation and selected message."""
        if not self.current_messages:
            messagebox.showerror("Error", "Please fetch a conversation first")
            return

        if self.selected_message_index < 0:
            messagebox.showerror("Error", "Please select a message to reply to from the list above")
            return

        # Update AI client with current model selection for OpenRouter
        if self.current_ai_provider == "openrouter":
            selected_model = self.model_var.get()
            if not selected_model:
                messagebox.showerror("Error", "Please select an OpenRouter model first")
                return
            deepseek_client.set_provider("openrouter", selected_model)
            self.current_ai_model = selected_model

        # Validate API key for current provider
        is_valid, error_msg = deepseek_client.validate_api_key()
        if not is_valid:
            messagebox.showerror("API Error", f"AI API issue: {error_msg}")
            return

        def generate_reply():
            self.show_progress(True)

            # Get selected message info for status
            selected_msg = self.current_messages[self.selected_message_index]
            sender = "You" if selected_msg.sender == self.current_user_email else selected_msg.sender

            # Show provider and model info
            provider_info = f"{self.current_ai_provider.title()}"
            if self.current_ai_provider == "openrouter":
                provider_info += f" ({self.current_ai_model})"

            self.update_status(f"Generating AI reply using {provider_info} to message from {sender}...")

            # Format conversation for AI (full context)
            formatted_conv = conversation_retriever.format_conversation(
                self.current_messages, self.current_user_email
            )

            # Add context about which message we're replying to
            selected_message_context = f"\n\n[REPLYING TO MESSAGE #{self.selected_message_index + 1} FROM {sender}]"
            formatted_conv += selected_message_context

            # Generate reply
            success, reply, error_msg = deepseek_client.generate_reply(formatted_conv)

            self.show_progress(False)

            if success:
                self.generated_reply = reply
                self.reply_text.delete(1.0, tk.END)
                self.reply_text.insert(1.0, reply)
                self.update_status(f"AI reply generated for message from {sender}")
            else:
                self.update_status("Failed to generate reply")
                messagebox.showerror("AI Error", f"Failed to generate reply: {error_msg}")

        # Run in separate thread
        threading.Thread(target=generate_reply, daemon=True).start()

    def _send_reply(self) -> None:
        """Send the generated reply."""
        if not self.current_user_email or not self.current_password:
            messagebox.showerror("Error", "Please authenticate with Gmail first")
            return

        if not self.current_target_email:
            messagebox.showerror("Error", "No target email address")
            return

        reply_text = self.reply_text.get(1.0, tk.END).strip()
        if not reply_text:
            messagebox.showerror("Error", "No reply text to send")
            return

        # Check if a message is selected
        if self.selected_message_index < 0:
            messagebox.showerror("Error", "Please select a message to reply to from the list above")
            return

        # Get subject and message ID from selected message
        selected_msg = self.current_messages[self.selected_message_index]
        subject = "Re: " + selected_msg.subject
        if subject.startswith("Re: Re:"):
            subject = subject[4:]  # Remove duplicate "Re: "

        # Confirm sending
        sender = "You" if selected_msg.sender == self.current_user_email else selected_msg.sender
        if not messagebox.askyesno("Confirm Send", f"Send reply to {self.current_target_email}?\n\nReplying to message from: {sender}"):
            return

        def send_reply():
            self.show_progress(True)
            self.update_status("Sending reply...")

            # Get message ID for threading from selected message
            reply_to_id = selected_msg.message_id

            success, error_msg = email_ops.send_email(
                self.current_user_email, self.current_password,
                self.current_target_email, subject, reply_text, reply_to_id
            )

            self.show_progress(False)

            if success:
                self.update_status("Reply sent successfully")
                messagebox.showinfo("Success", "Reply sent successfully!")
                # Clear the reply text
                self.reply_text.delete(1.0, tk.END)
            else:
                self.update_status("Failed to send reply")
                messagebox.showerror("Send Error", f"Failed to send reply: {error_msg}")

        # Run in separate thread
        threading.Thread(target=send_reply, daemon=True).start()

    @handle_exceptions(context="draft saving", user_action="saving draft")
    def _save_draft(self) -> None:
        """Save the current reply as a draft both locally and to Gmail."""
        if not self.current_target_email:
            messagebox.showerror("Error", "No target email address")
            return

        reply_text = self.reply_text.get(1.0, tk.END).strip()
        if not reply_text:
            messagebox.showerror("Error", "No reply text to save")
            return

        if not self.current_user_email or not self.current_password:
            messagebox.showerror("Error", "Please authenticate with Gmail first")
            return

        # Check if a message is selected
        if self.selected_message_index < 0:
            messagebox.showerror("Error", "Please select a message to reply to from the list above")
            return

        # Get subject and message ID from selected message
        selected_msg = self.current_messages[self.selected_message_index]
        subject = "Re: " + selected_msg.subject
        if subject.startswith("Re: Re:"):
            subject = subject[4:]  # Remove duplicate "Re: "

        # Get message ID for threading from selected message
        reply_to_id = selected_msg.message_id

        # Update status
        self.update_status("Saving draft locally and to Gmail...")

        # Disable save button during operation
        self.save_draft_btn.config(state='disabled')

        def save_draft():
            """Save draft in background thread."""
            try:
                # Save draft both locally and to Gmail
                success, filename, status_msg = email_ops.save_draft(
                    self.current_user_email, self.current_target_email,
                    subject, reply_text, reply_to_id, self.current_password
                )

                # Update UI in main thread
                self.root.after(0, lambda: self._handle_draft_save_result(success, filename, status_msg))

            except Exception as e:
                logger.error(f"Error in draft save thread: {str(e)}")
                self.root.after(0, lambda: self._handle_draft_save_result(False, "", str(e)))

        # Run in separate thread
        threading.Thread(target=save_draft, daemon=True).start()

    def _handle_draft_save_result(self, success: bool, filename: str, status_msg: str) -> None:
        """Handle the result of draft save operation."""
        # Re-enable save button
        self.save_draft_btn.config(state='normal')

        if success:
            self.update_status(f"Draft saved: {filename}")
            messagebox.showinfo("Success", f"Draft saved successfully!\n\n{status_msg}")
        else:
            self.update_status("Failed to save draft")
            messagebox.showerror("Save Error", f"Failed to save draft: {status_msg}")

    def run(self) -> None:
        """Run the GUI application."""
        try:
            logger.info("Starting EMDrafter GUI application")
            self.root.mainloop()
        except Exception as e:
            logger.error(f"GUI application error: {str(e)}")
            messagebox.showerror("Application Error", f"An error occurred: {str(e)}")
        finally:
            # Cleanup
            gmail_auth.logout()
            logger.info("EMDrafter GUI application closed")

def main():
    """Main entry point for the application."""
    try:
        # Create and run the GUI application
        app = EMDrafterGUI()
        app.run()
    except Exception as e:
        logger.error(f"Failed to start application: {str(e)}")
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    main()
