#!/usr/bin/env python3
"""
Test script for AI provider functionality in EMDrafter.
Tests both Deepseek and OpenRouter integration.
"""

import os
import sys
from deepseek_client import ai_client

def test_deepseek():
    """Test Deepseek provider functionality."""
    print("=== Testing Deepseek Provider ===")
    
    # Set provider
    ai_client.set_provider("deepseek")
    print(f"✓ Set provider to: {ai_client.current_provider}")
    print(f"✓ Using model: {ai_client.current_model}")
    
    # Test API key validation
    is_valid, error_msg = ai_client.validate_api_key("deepseek")
    if is_valid:
        print("✓ Deepseek API key is valid")
    else:
        print(f"✗ Deepseek API key validation failed: {error_msg}")
    
    return is_valid

def test_openrouter():
    """Test OpenRouter provider functionality."""
    print("\n=== Testing OpenRouter Provider ===")
    
    # Test model loading
    success, models, error_msg = ai_client.get_available_openrouter_models()
    if success:
        print(f"✓ Loaded {len(models)} OpenRouter models")
        if models:
            print(f"✓ Sample models: {[m['id'] for m in models[:3]]}")
            
            # Set provider with first model
            first_model = models[0]['id']
            ai_client.set_provider("openrouter", first_model)
            print(f"✓ Set provider to: {ai_client.current_provider}")
            print(f"✓ Using model: {ai_client.current_model}")
            
            # Test API key validation
            is_valid, error_msg = ai_client.validate_api_key("openrouter")
            if is_valid:
                print("✓ OpenRouter API key is valid")
            else:
                print(f"✗ OpenRouter API key validation failed: {error_msg}")
            
            return is_valid
        else:
            print("✗ No models returned from OpenRouter")
            return False
    else:
        print(f"✗ Failed to load OpenRouter models: {error_msg}")
        return False

def main():
    """Main test function."""
    print("EMDrafter AI Provider Test")
    print("=" * 40)
    
    # Check environment
    deepseek_key = os.getenv('DEEPSEEK_API_KEY')
    openrouter_key = os.getenv('OPENROUTER_API_KEY')
    
    print(f"Deepseek API Key: {'✓ Set' if deepseek_key else '✗ Not set'}")
    print(f"OpenRouter API Key: {'✓ Set' if openrouter_key else '✗ Not set'}")
    print()
    
    results = []
    
    # Test Deepseek if key is available
    if deepseek_key:
        results.append(("Deepseek", test_deepseek()))
    else:
        print("Skipping Deepseek test - no API key")
        results.append(("Deepseek", False))
    
    # Test OpenRouter if key is available
    if openrouter_key:
        results.append(("OpenRouter", test_openrouter()))
    else:
        print("Skipping OpenRouter test - no API key")
        results.append(("OpenRouter", False))
    
    # Summary
    print("\n=== Test Summary ===")
    for provider, success in results:
        status = "✓ PASS" if success else "✗ FAIL"
        print(f"{provider}: {status}")
    
    # Overall result
    all_passed = all(result[1] for result in results if os.getenv(f'{result[0].upper()}_API_KEY'))
    if all_passed:
        print("\n🎉 All available providers working correctly!")
        return 0
    else:
        print("\n❌ Some providers failed - check API keys and configuration")
        return 1

if __name__ == "__main__":
    sys.exit(main())
