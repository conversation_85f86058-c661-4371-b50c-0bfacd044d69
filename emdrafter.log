2025-06-26 22:38:05,064 - email_operations - INFO - Created drafts folder: drafts
2025-06-26 22:38:06,046 - email_operations - INFO - Draft saved: draft_20250626_223806_Test_Subject.json
2025-06-26 22:38:06,071 - email_operations - INFO - Draft loaded: draft_20250626_223806_Test_Subject.json
2025-06-26 22:38:06,074 - error_handler - ERROR - Error #1 in test_context while testing: Test error
Traceback (most recent call last):
  File "L:\AIApps\VSCodeAPPs\EMDrafter\test_emdrafter.py", line 313, in test_error_logging
    raise ValueError("Test error")
ValueError: Test error
2025-06-26 22:38:06,074 - error_handler - ERROR - Error #1: Test error
Traceback (most recent call last):
  File "L:\AIApps\VSCodeAPPs\EMDrafter\test_emdrafter.py", line 328, in test_error_summary
    raise ValueError("Test error")
ValueError: Test error
2025-06-26 22:38:48,271 - email_operations - INFO - Draft saved: draft_20250626_223848_Test_Subject.json
2025-06-26 22:38:48,289 - email_operations - INFO - Draft loaded: draft_20250626_223848_Test_Subject.json
2025-06-26 22:38:48,301 - error_handler - ERROR - Error #1 in test_context while testing: Test error
Traceback (most recent call last):
  File "L:\AIApps\VSCodeAPPs\EMDrafter\test_emdrafter.py", line 312, in test_error_logging
    raise ValueError("Test error")
ValueError: Test error
2025-06-26 22:38:48,303 - error_handler - ERROR - Error #1: Test error
Traceback (most recent call last):
  File "L:\AIApps\VSCodeAPPs\EMDrafter\test_emdrafter.py", line 327, in test_error_summary
    raise ValueError("Test error")
ValueError: Test error
2025-06-26 22:42:56,549 - __main__ - INFO - ==================================================
2025-06-26 22:42:56,549 - __main__ - INFO - Starting EMDrafter v1.0.0
2025-06-26 22:42:56,549 - __main__ - INFO - ==================================================
2025-06-26 22:42:56,924 - __main__ - INFO - All dependencies are available
2025-06-26 22:42:56,924 - __main__ - INFO - Configuration is valid
2025-06-26 22:42:56,924 - __main__ - INFO - Starting GUI application...
2025-06-26 22:42:59,362 - main_gui - INFO - Status: Ready
2025-06-26 22:42:59,362 - main_gui - INFO - Starting EMDrafter GUI application
2025-06-26 22:45:05,447 - main_gui - INFO - Status: Testing authentication...
2025-06-26 22:45:11,635 - gmail_auth - ERROR - IMAP authentication error: b'[ALERT] Application-specific password required: https://support.google.com/accounts/answer/185833 (Failure)'
2025-06-26 22:45:11,650 - main_gui - INFO - Status: Authentication failed
2025-06-26 22:48:14,288 - main_gui - INFO - Status: Testing authentication...
2025-06-26 22:48:18,571 - gmail_auth - ERROR - SMTP connection error: 'SMTP' object has no attribute 'settimeout'
2025-06-26 22:48:18,571 - main_gui - INFO - Status: Authentication failed
2025-06-26 22:51:43,228 - gmail_auth - INFO - Successfully logged out
2025-06-26 22:51:43,255 - main_gui - INFO - EMDrafter GUI application closed
2025-06-26 22:51:43,256 - __main__ - INFO - Application closed normally
2025-06-26 22:52:32,760 - __main__ - INFO - ==================================================
2025-06-26 22:52:32,760 - __main__ - INFO - Starting EMDrafter v1.0.0
2025-06-26 22:52:32,760 - __main__ - INFO - ==================================================
2025-06-26 22:52:34,539 - __main__ - INFO - All dependencies are available
2025-06-26 22:52:34,539 - __main__ - INFO - Configuration is valid
2025-06-26 22:52:34,539 - __main__ - INFO - Starting GUI application...
2025-06-26 22:52:35,708 - main_gui - INFO - Status: Ready
2025-06-26 22:52:35,729 - main_gui - INFO - Starting EMDrafter GUI application
2025-06-26 22:54:14,556 - __main__ - INFO - ==================================================
2025-06-26 22:54:14,556 - __main__ - INFO - Starting EMDrafter v1.0.0
2025-06-26 22:54:14,556 - __main__ - INFO - ==================================================
2025-06-26 22:54:14,791 - __main__ - INFO - All dependencies are available
2025-06-26 22:54:14,791 - __main__ - INFO - Configuration is valid
2025-06-26 22:54:14,791 - __main__ - INFO - Starting GUI application...
2025-06-26 22:54:15,119 - main_gui - INFO - Status: Ready
2025-06-26 22:54:15,119 - main_gui - INFO - Starting EMDrafter GUI application
2025-06-26 22:54:39,225 - main_gui - INFO - Status: Testing authentication...
2025-06-26 22:54:43,163 - gmail_auth - INFO - Successfully <NAME_EMAIL>
2025-06-26 22:54:43,163 - main_gui - INFO - Status: Authentication successful
2025-06-26 22:54:56,436 - main_gui - INFO - Status: Fetching conversation...
2025-06-26 22:54:59,436 - email_retrieval - INFO - Retrieved 5 messages from <NAME_EMAIL>
2025-06-26 22:54:59,608 - main_gui - INFO - Status: Found 5 messages in conversation
2025-06-26 22:56:13,728 - main_gui - INFO - Status: Generating AI reply...
2025-06-26 22:56:13,728 - deepseek_client - INFO - Making API request to Deepseek
2025-06-26 22:56:28,697 - deepseek_client - INFO - Successfully generated AI reply
2025-06-26 22:56:28,713 - main_gui - INFO - Status: AI reply generated successfully
2025-06-26 22:56:48,479 - email_operations - INFO - Draft saved: draft_20250626_225648_Re_Information_needed.json
2025-06-26 22:56:48,479 - main_gui - INFO - Status: Draft saved as draft_20250626_225648_Re_Information_needed.json
2025-06-26 23:00:21,937 - main_gui - INFO - Status: Fetching conversation...
2025-06-26 23:00:25,390 - email_retrieval - INFO - Retrieved 2 messages from <NAME_EMAIL>
2025-06-26 23:00:25,796 - main_gui - INFO - Status: Found 2 messages in conversation
2025-06-26 23:01:00,548 - main_gui - INFO - Status: Generating AI reply...
2025-06-26 23:01:00,564 - deepseek_client - INFO - Making API request to Deepseek
2025-06-26 23:01:11,861 - deepseek_client - INFO - Successfully generated AI reply
2025-06-26 23:01:11,892 - main_gui - INFO - Status: AI reply generated successfully
2025-06-26 23:02:15,573 - gmail_auth - INFO - Successfully logged out
2025-06-26 23:02:15,573 - main_gui - INFO - EMDrafter GUI application closed
2025-06-26 23:02:15,573 - __main__ - INFO - Application closed normally
2025-06-26 23:15:01,777 - __main__ - INFO - ==================================================
2025-06-26 23:15:01,777 - __main__ - INFO - Starting EMDrafter v1.0.0
2025-06-26 23:15:01,777 - __main__ - INFO - ==================================================
2025-06-26 23:15:02,027 - __main__ - INFO - All dependencies are available
2025-06-26 23:15:02,027 - __main__ - INFO - Configuration is valid
2025-06-26 23:15:02,027 - __main__ - INFO - Starting GUI application...
2025-06-26 23:15:02,277 - main_gui - INFO - Status: Ready
2025-06-26 23:15:02,277 - main_gui - INFO - Starting EMDrafter GUI application
2025-06-26 23:15:14,693 - gmail_auth - INFO - Successfully logged out
2025-06-26 23:15:14,693 - main_gui - INFO - EMDrafter GUI application closed
2025-06-26 23:15:14,693 - __main__ - INFO - Application closed normally
2025-06-26 23:25:19,222 - __main__ - INFO - ==================================================
2025-06-26 23:25:19,222 - __main__ - INFO - Starting EMDrafter v1.0.0
2025-06-26 23:25:19,222 - __main__ - INFO - ==================================================
2025-06-26 23:25:19,503 - __main__ - INFO - All dependencies are available
2025-06-26 23:25:19,503 - __main__ - INFO - Configuration is valid
2025-06-26 23:25:19,503 - __main__ - INFO - Starting GUI application...
2025-06-26 23:25:19,706 - main_gui - INFO - Status: Ready
2025-06-26 23:25:19,706 - main_gui - INFO - Starting EMDrafter GUI application
2025-06-26 23:25:54,088 - main_gui - INFO - Status: Testing authentication...
2025-06-26 23:26:00,541 - gmail_auth - INFO - Successfully <NAME_EMAIL>
2025-06-26 23:26:00,541 - main_gui - INFO - Status: Authentication successful
2025-06-26 23:26:13,010 - main_gui - INFO - Status: Fetching conversation...
2025-06-26 23:26:20,869 - email_retrieval - INFO - Retrieved 5 messages from <NAME_EMAIL>
2025-06-26 23:26:20,885 - main_gui - INFO - Status: Found 5 messages in conversation
2025-06-26 23:26:49,791 - main_gui - INFO - Status: Generating AI reply...
2025-06-26 23:26:49,791 - deepseek_client - INFO - Making API request to Deepseek
2025-06-26 23:27:05,479 - deepseek_client - INFO - Successfully generated AI reply
2025-06-26 23:27:05,495 - main_gui - INFO - Status: AI reply generated successfully
2025-06-26 23:27:21,331 - main_gui - INFO - Status: Saving draft locally and to Gmail...
2025-06-26 23:27:21,331 - email_operations - INFO - Draft saved locally: draft_20250626_232721_Re_Information_needed.json
2025-06-26 23:27:34,837 - email_operations - INFO - Draft successfully saved to Gmail folder: [Gmail]/Drafts
2025-06-26 23:27:35,540 - email_operations - INFO - Draft saved to Gmail Drafts folder
2025-06-26 23:27:35,555 - main_gui - INFO - Status: Draft saved: draft_20250626_232721_Re_Information_needed.json
2025-06-26 23:28:57,922 - main_gui - INFO - Status: Sending reply...
2025-06-26 23:29:01,556 - email_operations - INFO - Email sent <NAME_EMAIL>
2025-06-26 23:29:01,556 - main_gui - INFO - Status: Reply sent successfully
2025-06-26 23:31:04,739 - gmail_auth - INFO - Successfully logged out
2025-06-26 23:31:04,739 - main_gui - INFO - EMDrafter GUI application closed
2025-06-26 23:31:04,739 - __main__ - INFO - Application closed normally
2025-06-26 23:31:53,383 - __main__ - INFO - ==================================================
2025-06-26 23:31:53,383 - __main__ - INFO - Starting EMDrafter v1.0.0
2025-06-26 23:31:53,383 - __main__ - INFO - ==================================================
2025-06-26 23:31:53,649 - __main__ - INFO - All dependencies are available
2025-06-26 23:31:53,649 - __main__ - INFO - Configuration is valid
2025-06-26 23:31:53,649 - __main__ - INFO - Starting GUI application...
2025-06-26 23:31:53,930 - main_gui - INFO - Status: Ready
2025-06-26 23:31:53,946 - main_gui - INFO - Starting EMDrafter GUI application
2025-06-26 23:32:17,686 - main_gui - INFO - Status: Testing authentication...
2025-06-26 23:32:21,717 - gmail_auth - INFO - Successfully <NAME_EMAIL>
2025-06-26 23:32:21,722 - main_gui - INFO - Status: Authentication successful
2025-06-26 23:32:56,155 - main_gui - INFO - Status: Fetching conversation...
2025-06-26 23:32:58,342 - email_retrieval - INFO - Retrieved 1 messages from <NAME_EMAIL>
2025-06-26 23:32:59,108 - main_gui - INFO - Status: Found 1 messages in conversation
2025-06-26 23:34:08,400 - main_gui - INFO - Status: Fetching conversation...
2025-06-26 23:34:18,228 - email_retrieval - INFO - Retrieved 10 messages from <NAME_EMAIL>
2025-06-26 23:34:18,291 - main_gui - INFO - Status: Found 10 messages in conversation
2025-06-26 23:34:54,303 - gmail_auth - INFO - Successfully logged out
2025-06-26 23:34:54,303 - main_gui - INFO - EMDrafter GUI application closed
2025-06-26 23:34:54,319 - __main__ - INFO - Application closed normally
2025-06-26 23:49:17,981 - __main__ - INFO - ==================================================
2025-06-26 23:49:17,981 - __main__ - INFO - Starting EMDrafter v1.0.0
2025-06-26 23:49:17,981 - __main__ - INFO - ==================================================
2025-06-26 23:49:18,231 - __main__ - INFO - All dependencies are available
2025-06-26 23:49:18,231 - __main__ - INFO - Configuration is valid
2025-06-26 23:49:18,231 - __main__ - INFO - Starting GUI application...
2025-06-26 23:49:18,560 - main_gui - INFO - Status: Ready
2025-06-26 23:49:18,560 - main_gui - INFO - Starting EMDrafter GUI application
2025-06-26 23:49:40,945 - main_gui - INFO - Status: Testing authentication...
2025-06-26 23:49:45,023 - gmail_auth - INFO - Successfully <NAME_EMAIL>
2025-06-26 23:49:45,039 - main_gui - INFO - Status: Authentication successful
2025-06-26 23:50:33,159 - main_gui - INFO - Status: Fetching conversation...
2025-06-26 23:50:35,393 - email_retrieval - INFO - Retrieved 1 messages from <NAME_EMAIL>
2025-06-26 23:50:35,471 - main_gui - INFO - Status: Found 1 message in conversation
2025-06-26 23:51:01,276 - main_gui - INFO - Status: Fetching conversation...
2025-06-26 23:51:06,198 - email_retrieval - INFO - Retrieved 10 messages from <NAME_EMAIL>
2025-06-26 23:51:10,214 - main_gui - INFO - Status: Found 10 messages. Select a message above to reply to.
2025-06-26 23:51:46,287 - main_gui - INFO - Status: Selected message from TAAFT - There's An AI For That <<EMAIL>> - 2025-06-17 09:06
2025-06-26 23:51:50,475 - main_gui - INFO - Status: Selected message from TAAFT - There's An AI For That <<EMAIL>> - 2025-06-18 10:44
2025-06-26 23:51:52,725 - main_gui - INFO - Status: Selected message from TAAFT - There's An AI For That <<EMAIL>> - 2025-06-19 11:31
2025-06-26 23:51:55,334 - main_gui - INFO - Status: Selected message from TAAFT - There's An AI For That <<EMAIL>> - 2025-06-20 10:58
2025-06-26 23:51:59,506 - main_gui - INFO - Status: Selected message from TAAFT - There's An AI For That <<EMAIL>> - 2025-06-17 09:06
2025-06-26 23:52:09,928 - main_gui - INFO - Status: Selected message from TAAFT - There's An AI For That <<EMAIL>> - 2025-06-18 10:44
2025-06-26 23:52:22,446 - main_gui - INFO - Status: Selected message from TAAFT - There's An AI For That <<EMAIL>> - 2025-06-19 11:31
2025-06-26 23:52:28,852 - main_gui - INFO - Status: Generating AI reply to message from TAAFT - There's An AI For That <<EMAIL>>...
2025-06-26 23:52:28,868 - deepseek_client - WARNING - Conversation too long (106295 chars), truncating
2025-06-26 23:52:28,868 - deepseek_client - INFO - Making API request to Deepseek
2025-06-26 23:52:39,859 - deepseek_client - INFO - Successfully generated AI reply
2025-06-26 23:52:39,890 - main_gui - INFO - Status: AI reply generated for message from TAAFT - There's An AI For That <<EMAIL>>
2025-06-26 23:53:22,679 - main_gui - INFO - Status: Selected message from TAAFT - There's An AI For That <<EMAIL>> - 2025-06-20 10:58
2025-06-26 23:53:23,851 - main_gui - INFO - Status: Selected message from TAAFT - There's An AI For That <<EMAIL>> - 2025-06-19 11:31
2025-06-26 23:53:24,804 - main_gui - INFO - Status: Selected message from TAAFT - There's An AI For That <<EMAIL>> - 2025-06-18 10:44
2025-06-26 23:53:25,429 - main_gui - INFO - Status: Selected message from TAAFT - There's An AI For That <<EMAIL>> - 2025-06-17 09:06
2025-06-26 23:53:26,132 - main_gui - INFO - Status: Selected message from TAAFT - There's An AI For That <<EMAIL>> - 2025-06-19 11:31
2025-06-26 23:53:28,273 - main_gui - INFO - Status: Selected message from TAAFT - There's An AI For That <<EMAIL>> - 2025-06-20 10:58
2025-06-26 23:53:44,446 - main_gui - INFO - Status: Selected message from TAAFT - There's An AI For That <<EMAIL>> - 2025-06-19 11:31
2025-06-26 23:53:46,727 - main_gui - INFO - Status: Selected message from TAAFT - There's An AI For That <<EMAIL>> - 2025-06-17 09:06
2025-06-26 23:54:13,933 - main_gui - INFO - Status: Fetching conversation...
2025-06-26 23:54:16,948 - email_retrieval - INFO - Retrieved 5 messages from <NAME_EMAIL>
2025-06-26 23:54:17,011 - main_gui - INFO - Status: Found 5 messages. Select a message above to reply to.
2025-06-26 23:54:41,526 - main_gui - INFO - Status: Selected message from Awad Print <<EMAIL>> - 2023-07-21 23:53
2025-06-26 23:55:10,151 - main_gui - INFO - Status: Selected message from Awad Print <<EMAIL>> - 2025-06-26 22:36
2025-06-26 23:55:21,558 - main_gui - INFO - Status: Selected message from Awad Print <<EMAIL>> - 2025-06-26 16:38
2025-06-26 23:55:22,855 - main_gui - INFO - Status: Selected message from Awad Print <<EMAIL>> - 2025-06-26 22:36
2025-06-26 23:55:23,933 - main_gui - INFO - Status: Selected message from Awad Print <<EMAIL>> - 2025-06-26 16:38
2025-06-26 23:55:24,573 - main_gui - INFO - Status: Selected message from Awad Print <<EMAIL>> - 2024-05-04 00:06
2025-06-26 23:55:25,151 - main_gui - INFO - Status: Selected message from Awad Print <<EMAIL>> - 2023-07-21 23:53
2025-06-26 23:55:25,995 - main_gui - INFO - Status: Selected message from Awad Print <<EMAIL>> - 2024-05-04 00:06
2025-06-26 23:55:26,948 - main_gui - INFO - Status: Selected message from Awad Print <<EMAIL>> - 2025-06-26 16:38
2025-06-26 23:55:27,761 - main_gui - INFO - Status: Selected message from Awad Print <<EMAIL>> - 2025-06-26 22:36
2025-06-26 23:55:34,433 - main_gui - INFO - Status: Selected message from Awad Print <<EMAIL>> - 2025-06-26 16:38
2025-06-26 23:55:44,854 - main_gui - INFO - Status: Generating AI reply to message from Awad Print <<EMAIL>>...
2025-06-26 23:55:44,854 - deepseek_client - INFO - Making API request to Deepseek
2025-06-26 23:55:56,693 - deepseek_client - INFO - Successfully generated AI reply
2025-06-26 23:55:56,724 - main_gui - INFO - Status: AI reply generated for message from Awad Print <<EMAIL>>
2025-06-26 23:56:13,927 - main_gui - INFO - Status: Selected message from Awad Print <<EMAIL>> - 2023-07-21 23:53
2025-06-26 23:56:27,615 - main_gui - INFO - Status: Generating AI reply to message from Awad Print <<EMAIL>>...
2025-06-26 23:56:27,615 - deepseek_client - INFO - Making API request to Deepseek
2025-06-26 23:56:40,412 - deepseek_client - INFO - Successfully generated AI reply
2025-06-26 23:56:40,427 - main_gui - INFO - Status: AI reply generated for message from Awad Print <<EMAIL>>
2025-06-26 23:56:58,427 - main_gui - INFO - Status: Saving draft locally and to Gmail...
2025-06-26 23:56:58,427 - email_operations - INFO - Draft saved locally: draft_20250626_235658_Re_Need_some_information_on_ChatGPT_and_email_comb.json
2025-06-26 23:57:00,583 - email_operations - INFO - Draft successfully saved to Gmail folder: [Gmail]/Drafts
2025-06-26 23:57:00,739 - email_operations - INFO - Draft saved to Gmail Drafts folder
2025-06-26 23:57:00,755 - main_gui - INFO - Status: Draft saved: draft_20250626_235658_Re_Need_some_information_on_ChatGPT_and_email_comb.json
2025-06-26 23:58:17,442 - gmail_auth - INFO - Successfully logged out
2025-06-26 23:58:17,449 - main_gui - INFO - EMDrafter GUI application closed
2025-06-26 23:58:17,452 - __main__ - INFO - Application closed normally
2025-06-27 00:30:23,716 - __main__ - INFO - ==================================================
2025-06-27 00:30:23,716 - __main__ - INFO - Starting EMDrafter v1.0.0
2025-06-27 00:30:23,716 - __main__ - INFO - ==================================================
2025-06-27 00:30:23,982 - __main__ - INFO - All dependencies are available
2025-06-27 00:30:23,982 - __main__ - INFO - Configuration is valid
2025-06-27 00:30:23,982 - __main__ - INFO - Starting GUI application...
2025-06-27 00:30:24,232 - main_gui - INFO - Status: Ready
2025-06-27 00:30:24,232 - main_gui - INFO - Starting EMDrafter GUI application
2025-06-27 00:31:59,587 - main_gui - INFO - Status: Testing authentication...
2025-06-27 00:32:05,002 - gmail_auth - INFO - Successfully <NAME_EMAIL>
2025-06-27 00:32:05,005 - main_gui - INFO - Status: Authentication successful
2025-06-27 00:32:10,440 - main_gui - INFO - Status: OpenRouter selected - click 'Load Models' to see available models
2025-06-27 00:32:12,773 - main_gui - INFO - Status: Loading OpenRouter models...
2025-06-27 00:32:13,671 - main_gui - INFO - Status: Loaded 316 OpenRouter models
2025-06-27 00:33:10,934 - main_gui - INFO - Status: Fetching conversation...
2025-06-27 00:33:14,565 - email_retrieval - INFO - Retrieved 3 messages from <NAME_EMAIL>
2025-06-27 00:33:14,718 - main_gui - INFO - Status: Found 3 messages. Select a message above to reply to.
2025-06-27 00:33:31,214 - main_gui - INFO - Status: Selected <NAME_EMAIL> - 2025-06-26 05:41
2025-06-27 00:33:45,272 - main_gui - INFO - Status: Selected message from Naeem Qous Qazah <<EMAIL>> - 2023-11-06 12:29
2025-06-27 00:33:51,948 - main_gui - INFO - Status: Selected <NAME_EMAIL> - 2025-06-26 12:29
2025-06-27 00:34:29,342 - main_gui - INFO - Status: Selected <NAME_EMAIL> - 2025-06-26 12:29
2025-06-27 00:34:40,551 - main_gui - INFO - Status: Switched to Deepseek provider
2025-06-27 00:34:42,473 - main_gui - INFO - Status: Selected <NAME_EMAIL> - 2025-06-26 12:29
2025-06-27 00:34:47,245 - main_gui - INFO - Status: Generating AI reply using Deepseek to <NAME_EMAIL>...
2025-06-27 00:34:47,261 - deepseek_client - INFO - Making API request to Deepseek
2025-06-27 00:34:56,710 - deepseek_client - INFO - Successfully generated AI reply
2025-06-27 00:34:56,710 - main_gui - INFO - Status: AI reply generated for <NAME_EMAIL>
2025-06-27 00:36:48,579 - main_gui - INFO - Status: Saving draft locally and to Gmail...
2025-06-27 00:36:48,600 - email_operations - INFO - Draft saved locally: draft_20250627_003648_Re_Information_needed.json
2025-06-27 00:36:51,498 - email_operations - INFO - Draft successfully saved to Gmail folder: [Gmail]/Drafts
2025-06-27 00:36:51,753 - email_operations - INFO - Draft saved to Gmail Drafts folder
2025-06-27 00:36:53,169 - main_gui - INFO - Status: Draft saved: draft_20250627_003648_Re_Information_needed.json
2025-06-27 00:38:33,620 - gmail_auth - INFO - Successfully logged out
2025-06-27 00:38:33,620 - main_gui - INFO - EMDrafter GUI application closed
2025-06-27 00:38:33,620 - __main__ - INFO - Application closed normally
2025-06-27 11:06:34,200 - __main__ - INFO - ==================================================
2025-06-27 11:06:34,200 - __main__ - INFO - Starting EMDrafter v1.0.0
2025-06-27 11:06:34,201 - __main__ - INFO - ==================================================
2025-06-27 11:06:34,528 - __main__ - INFO - All dependencies are available
2025-06-27 11:06:34,528 - __main__ - INFO - Configuration is valid
2025-06-27 11:06:34,528 - __main__ - INFO - Starting GUI application...
2025-06-27 11:06:35,680 - main_gui - INFO - Status: Ready
2025-06-27 11:06:35,680 - main_gui - INFO - Starting EMDrafter GUI application
2025-06-27 11:07:15,023 - main_gui - INFO - Status: Testing authentication...
2025-06-27 11:07:20,042 - gmail_auth - INFO - Successfully <NAME_EMAIL>
2025-06-27 11:07:20,044 - main_gui - INFO - Status: Authentication successful
2025-06-27 11:08:13,805 - main_gui - INFO - Status: Fetching conversation...
2025-06-27 11:08:17,055 - email_retrieval - INFO - Retrieved 6 messages from <NAME_EMAIL>
2025-06-27 11:08:17,136 - main_gui - INFO - Status: Found 6 messages. Select a message above to reply to.
2025-06-27 11:08:30,554 - main_gui - INFO - Status: Selected message from Awad Print <<EMAIL>> - 2023-02-16 13:08
2025-06-27 11:08:34,058 - main_gui - INFO - Status: Selected message from Awad Print <<EMAIL>> - 2023-07-21 23:53
2025-06-27 11:09:20,840 - main_gui - INFO - Status: Selected message from Awad Print <<EMAIL>> - 2025-06-27 00:37
2025-06-27 11:09:32,389 - main_gui - INFO - Status: Generating AI reply using Deepseek to message from Awad Print <<EMAIL>>...
2025-06-27 11:09:32,390 - deepseek_client - INFO - Making API request to Deepseek
2025-06-27 11:09:48,347 - deepseek_client - INFO - Successfully generated AI reply
2025-06-27 11:09:48,362 - main_gui - INFO - Status: AI reply generated for message from Awad Print <<EMAIL>>
2025-06-27 11:11:17,847 - main_gui - INFO - Status: Selected message from Awad Print <<EMAIL>> - 2025-06-27 00:37
2025-06-27 11:11:29,276 - main_gui - INFO - Status: Sending reply...
2025-06-27 11:11:33,281 - email_operations - INFO - Email sent <NAME_EMAIL>
2025-06-27 11:11:33,284 - main_gui - INFO - Status: Reply sent successfully
2025-06-27 11:12:23,827 - main_gui - INFO - Status: OpenRouter selected - click 'Load Models' to see available models
2025-06-27 11:12:25,199 - main_gui - INFO - Status: Loading OpenRouter models...
2025-06-27 11:12:25,865 - main_gui - INFO - Status: Loaded 317 OpenRouter models
2025-06-27 11:13:30,492 - main_gui - INFO - Status: Selected message from Awad Print <<EMAIL>> - 2025-06-26 16:38
2025-06-27 11:14:03,393 - gmail_auth - INFO - Successfully logged out
2025-06-27 11:14:03,393 - main_gui - INFO - EMDrafter GUI application closed
2025-06-27 11:14:03,393 - __main__ - INFO - Application closed normally
